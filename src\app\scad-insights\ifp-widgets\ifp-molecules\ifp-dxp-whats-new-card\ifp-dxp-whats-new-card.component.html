<div class="ifp-whats-new-card ifp-whats-new-card--dxp" #card>
  <ifp-card>
    <div class="ifp-whats-new-card__header">
      <div class="ifp-whats-new-card__icon-wrapper">
        <!-- Checkbox moved to top-left -->
        <app-ifp-checkbox
          [appIfpTooltip]="'Check to select indicator' | translate"
          [extraSpaceTop]="20"
          [type]="'checkbox'"
          [enableFor]="true"
          [hideLabel]="true"
          [id]="id"
          (checkedEvent)="selectNode($event)"
          [defualtChecked]="isSelected"
          class="ifp-whats-new-card__header-checkbox">
        </app-ifp-checkbox>
        <!-- Single DXP domain icon -->
        <ifp-domain-icon [useDxpIcon]="true" [width]="24" [height]="24"></ifp-domain-icon>
      </div>
      <div class="ifp-whats-new-card__buttons">
        <div class="ifp-whats-new-card__rect ifp-whats-new-card__rect--small {{analyticsClasses.collapsedCard}}"
          [ngClass]="{'ifp-whats-new-card__fill': this.small}"
           (click)="resize(true)" id="card-small"></div>
        <div class="ifp-whats-new-card__rect ifp-whats-new-card__rect--large {{analyticsClasses.expandedCard}}"
          [ngClass]="{'ifp-whats-new-card__fill': !this.small}"
           (click)="resize(false)" id="card-large"></div>
        <ifp-button class="ifp-whats-new-card__btn ifp-open-new" [buttonColor]="'black'"
          [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-link-curve'" [link]="false"
          (click)="openUrl('/statistics-insights/'+contentType+'/'+id)" id="open-new"></ifp-button>
      </div>
    </div>
    <div class="ifp-whats-new-card__body" [ngClass]="{'ifp-whats-new-card__body--expanded': !small}">
      <div class="ifp-whats-new-card__left">
        <div class="ifp-whats-new-card__value-range">
          <div class="ifp-whats-new-card__value" *ngIf="value || +value === 0">
            {{(isValueBelowThreshold() ? '< ' : '') + (getDisplayValue() | shortNumber)}}
          </div>
          <span *ngIf="data?.unit" class="ifp-whats-new-card__unit">{{data?.unit}}</span>
          <div class="ifp-whats-new-card__range" *ngIf="range">
            {{range | translate}}
          </div>
        </div>
        <div class="ifp-whats-new-card__name" *ngIf="name" [appIfpTooltip]="name"
          [ngClass]="{'ifp-whats-new-card__name--census' : chartData?.[0]?.data?.length <= 1}"
          [disableTooltip]="name ? name.length < textLimit : true" [delay]="3000">
          {{name ? name.charAt(0).toUpperCase() + name.slice(1): '' | translate}}
        </div>
        <!-- Component subtitle display -->
        <div class="ifp-whats-new-card__subtitle" *ngIf="subtitle" [appIfpTooltip]="subtitle"
          [disableTooltip]="subtitle.length < textLimit" [delay]="3000">
          {{subtitle | translate}}
        </div>
        @if (baseDate) {
        <div class="ifp-whats-new-card__txt-icon">
          <ifp-icon-text [icon]="'ifp-icon-calender'" [text]="baseDate" [key]="'Updated date'"></ifp-icon-text>
          @if (isDxp()) {
            <ifp-sync-button [syncStatus]="syncStatus()" class="ifp-whats-new-card__sync"></ifp-sync-button>
          }
        </div>
        }
      </div>
      <div class="ifp-whats-new-card__right" *ngIf="!small">
        <div class="ifp-whats-new-card__name" *ngIf="name" @fadeInOut [appIfpTooltip]="name"
          [disableTooltip]="name ? name.length < textLimit : true" [delay]="3000">
          {{ name ? name.charAt(0).toUpperCase() + name.slice(1): '' | translate}}
        </div>
        <!-- DXP Chart Display -->
        <div class="ifp-whats-new-card__chart-container" *ngIf="chart">
          <ifp-highcharts
            *ngIf="chartData && chartData.length > 0"
            [height]="100"
            #chartRef
            [data]="chartData"
            [chartName]="'lineChart'"
            [chartClass]="''"
            [format]="format"
            [comparison]="comparison">
          </ifp-highcharts>
          <!-- Fallback message when no chart data is available -->
          <div *ngIf="!chartData || chartData.length === 0" class="ifp-whats-new-card__no-chart">
            <p>{{'No chart data available' | translate}}</p>
          </div>
        </div>
        <div class="ifp-whats-new-card__compare-legend" *ngIf="chart && chartData?.[0]?.data?.length > 1">
          <img src="../../../../assets/images/trg.png">
          <span class="ifp-whats-new-card__compare-text">{{'Compared Indicator'| translate}}</span>
        </div>
      </div>
    </div>

    <div class="ifp-whats-new-card__remove">
      <p>{{classification}}</p>
      <div class="ifp-whats-new-card__footer-icons">
        @if (remove) {
        <ifp-button class="ifp-whats-new-card__remove-btn" [buttonClass]="buttonClass.icon" [buttonColor]="buttonColor.black"
        [tooltipValue]="'Remove card'| translate" [iconClass]="'ifp-icon-trash'"
        (ifpClick)="removeEvent()"></ifp-button>
        }
      </div>
    </div>
  </ifp-card>
</div>
