import { Component, inject, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpWhatsNewCardComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-whats-new-card/ifp-whats-new-card.component";
import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { dxpApi } from '../dxp.constants';
import { ListingPageData, ListingPageItem } from '../dxp.interface';
import { IfpDxpWhatsNewCardComponent } from "src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-dxp-whats-new-card/ifp-dxp-whats-new-card.component";

@Component({
  selector: 'ifp-dxp-indicator',
  imports: [TranslateModule, IfpWhatsNewCardComponent, IfpDxpWhatsNewCardComponent],
  templateUrl: './dxp-indicator.component.html',
  styleUrl: './dxp-indicator.component.scss'
})
export class DxpIndicatorComponent implements OnInit {

  private readonly _subs: SubSink = new SubSink();
  private readonly _apiService = inject(ApiService);

  public dummyCardsIds = ['6691', '5965', '2415', '2424', '5858', '2420', '2421', '2425', '2422', '5867']

  public indicatorCards: ListingPageItem[] = [];
  


  ngOnInit(): void {
    this.getIndicatorList();
  }

  getIndicatorList() {
    this._subs.add(
      this._apiService.getMethodRequest(dxpApi.listEntityKpi, { status: 'pending' }, true).subscribe({
        next: (resp: ListingPageData) => {
          this.indicatorCards = resp?.data ?? [];
        }
      })
    );
  }

  dxpComponentData = {
    "objectId": "1a9719ce-e74f-4889-8e7b-ff2ea24750ae",
    "component_title": "Sample DXP Component",
    "component_subtitle": "Sample subtitle",
    "sourceAssetId": "ZXukWa6eQiWW0xYNhBKZrA",
    "sourceProductId": "5Jpdhn9PTmuGh5H4QXQvEw",
    "visualizationConfig": {
      "source_filter": {
        "groups": [
          {
            "conditions": [],
            "operator": "and"
          }
        ],
        "global_operator": "and"
      },
      "chart_configuration": {
        "x_axis": {
          "label": "X Axis Label",
          "axis": {
            "column": "month",
            "data_type": "int",
            "aggregator": "sum"
          }
        },
        "y_axis": {
          "label": "Y Axis Label",
          "axis": {
            "column": "month_name",
            "data_type": "string",
            "aggregator": "count"
          }
        },
        "filterPanel": []
      }
    },
    "updated": "2025-08-05T13:25:31.037Z",
    "createdAt": "2025-08-05T13:25:31.037Z",
    "createdById": "7d5ad35a-c737-40c4-aac9-4128ea3d69e9",
    "createdBy": {
      "name": "John Doe",
      "designation": "specialist",
      "email": "<EMAIL>"
    },
    "approvalRequest": {
      "id": "01d8fdba-ad12-48d1-a03b-c6ca960f6688",
      "status": "pending"
    },
    // Optional additional properties
    "value": 1250,
    "unit": "units",
    "thresholdValue": 1000,
    "chartData": [/* chart data array */]
  };

  onRemoveCard(event: any) {
    console.log('Card removed:', event);
  }

  onSelectIndicator(event: any) {
    console.log('Indicator selected:', event);
  }

  onCardResized(event: any) {
    console.log('Card resized:', event);
  }

}
