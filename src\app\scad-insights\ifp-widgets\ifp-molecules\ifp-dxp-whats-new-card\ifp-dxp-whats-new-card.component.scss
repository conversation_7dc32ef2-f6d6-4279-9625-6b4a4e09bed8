@use "../../../../../assets/ifp-styles/abstracts/index" as *;
@use "../../../../../assets/ifp-styles/components/_whats-new-card.scss";

.ifp-whats-new-card {
  &--dxp {
    .ifp-whats-new-card {
      &__value-range {
        margin-bottom: $spacer-3;
      }
      &__remove {
        width: 100%;
        margin-top: $spacer-3;
      }

      // Header layout adjustments for checkbox and icon
      &__icon-wrapper {
        display: flex;
        align-items: center;
        gap: $spacer-2;
      }

      // Checkbox styling in header
      &__header-checkbox {
        display: flex;
        align-items: center;
      }

      // Subtitle styling
      &__subtitle {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: $spacer-1;
        line-height: 1.4;
        font-weight: 400;
      }

      // Chart container styling
      &__chart-container {
        margin-top: $spacer-2;
        width: 100%;
      }

      // No chart data message
      &__no-chart {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100px;
        background-color: var(--background-light, #f8f9fa);
        border-radius: 4px;
        border: 1px dashed var(--border-color, #dee2e6);

        p {
          margin: 0;
          color: var(--text-muted, #6c757d);
          font-size: 0.875rem;
          font-style: italic;
        }
      }

      // Chart loading state
      &__chart-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100px;
        background-color: var(--background-light, #f8f9fa);
        border-radius: 4px;

        p {
          margin: $spacer-2 0 0 0;
          color: var(--text-muted, #6c757d);
          font-size: 0.875rem;
        }
      }

      // Loading spinner
      &__loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid var(--border-color, #dee2e6);
        border-top: 2px solid var(--primary-color, #007bff);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

// Spinner animation
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
