import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit } from '@angular/core';
import { IfpImgComponent } from '../../ifp-atoms/ifp-img/ifp-img.component';
import { Store } from '@ngrx/store';
import { selectDomainIconGetById } from 'src/app/scad-insights/store/domain-icon/domain-icon.selector';
import { AsyncPipe } from '@angular/common';

@Component({
    selector: 'ifp-domain-icon',
    templateUrl: './ifp-domain-icon.component.html',
    styleUrls: ['./ifp-domain-icon.component.scss'],
    imports: [IfpImgComponent, AsyncPipe],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class IfpDomainIconComponent implements OnInit, OnChanges{
@Input() domainName: string | any = '';
@Input() width = 18;
@Input() height = 18;
@Input() isTooltip: boolean = true;
@Input() onlyWhite: boolean = false;
@Input() useDxpIcon: boolean = false; // New input for DXP-specific icon behavior
public domainIconSelector$ = this.store.select(selectDomainIconGetById(this.domainName));
constructor(private store: Store) { }
ngOnInit() {
  this.domainIconSelector$ = this.store.select(selectDomainIconGetById(this.domainName));
}

ngOnChanges(): void {
  this.domainIconSelector$ = this.store.select(selectDomainIconGetById(this.domainName));
}


}
