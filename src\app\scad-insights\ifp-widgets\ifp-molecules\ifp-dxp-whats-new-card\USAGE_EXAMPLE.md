# IFP DXP What's New Card Component Usage

## Overview
The `ifp-dxp-whats-new-card` component displays DXP component data in a card format with expandable chart functionality. It accepts data as an input property and shows charts when the user clicks the expand button.

## Basic Usage

### In your component template:
```html
<ifp-dxp-whats-new-card 
  [data]="dxpComponentData"
  [contentType]="'dxp-component'"
  [index]="0"
  [small]="true"
  [remove]="true"
  [isSelected]="false"
  (crossClick)="onRemoveCard($event)"
  (selectIndicator)="onSelectIndicator($event)"
  (resized)="onCardResized($event)">
</ifp-dxp-whats-new-card>
```

### Sample Data Structure:
```typescript
export class YourComponent {
  dxpComponentData = {
    "objectId": "1a9719ce-e74f-4889-8e7b-ff2ea24750ae",
    "component_title": "Sample DXP Component",
    "component_subtitle": "This is a sample subtitle for the DXP component",
    "visualizationConfig": {
      "chart_configuration": {
        "x_axis": {
          "label": "X Axis Label",
          "axis": {
            "column": "month",
            "data_type": "int",
            "aggregator": "sum"
          }
        },
        "y_axis": {
          "label": "Y Axis Label",
          "axis": {
            "column": "month_name", 
            "data_type": "string",
            "aggregator": "count"
          }
        }
      },
      // Optional: Include chart data directly in visualization config
      "chartData": [
        {
          "color": "#007bff",
          "data": [
            [Date.UTC(2024, 0, 1), 45],
            [Date.UTC(2024, 1, 1), 52],
            [Date.UTC(2024, 2, 1), 38],
            [Date.UTC(2024, 3, 1), 67],
            [Date.UTC(2024, 4, 1), 73],
            [Date.UTC(2024, 5, 1), 81]
          ],
          "marker": {
            "radius": 4,
            "enabled": true
          },
          "name": "Sample Data"
        }
      ]
    },
    "updated": "2025-08-05T13:25:31.037Z",
    "createdBy": {
      "name": "John Doe",
      "designation": "specialist",
      "email": "<EMAIL>"
    },
    // Optional additional properties for display
    "value": 1250,
    "unit": "units",
    "thresholdValue": 1000,
    // Alternative: Direct chart data (takes precedence over visualizationConfig)
    "chartData": [
      {
        "color": "#28a745",
        "data": [
          [Date.UTC(2024, 0, 1), 45],
          [Date.UTC(2024, 1, 1), 52],
          [Date.UTC(2024, 2, 1), 38]
        ],
        "marker": {
          "radius": 4,
          "enabled": true
        }
      }
    ]
  };

  onRemoveCard(event: any) {
    console.log('Card removed:', event);
  }

  onSelectIndicator(event: any) {
    console.log('Indicator selected:', event);
  }

  onCardResized(event: any) {
    console.log('Card resized:', event);
    // event will be null when collapsed, or the index when expanded
  }
}
```

## Chart Functionality

### Expand/Collapse Behavior
- **Small View (Collapsed)**: Shows only the main data (title, subtitle, value, date)
- **Large View (Expanded)**: Shows the chart in addition to the main data
- **Chart Display**: Automatically shows when user clicks the expand button (large rectangle)
- **Chart Data Sources**: 
  1. `data.chartData` (highest priority)
  2. `data.visualizationConfig.chartData` (medium priority)
  3. Auto-generated sample data (fallback)

### Chart Data Format
The chart expects data in HighCharts format:
```typescript
chartData = [
  {
    color: '#007bff',           // Line color
    data: [                     // Array of [timestamp, value] pairs
      [Date.UTC(2024, 0, 1), 45],
      [Date.UTC(2024, 1, 1), 52],
      [Date.UTC(2024, 2, 1), 38]
    ],
    marker: {
      radius: 4,
      enabled: true
    },
    name: 'Data Series Name'    // Optional series name
  }
];
```

## Input Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `data` | `DxpComponentData \| null` | `null` | Main data object containing DXP component data |
| `classification` | `string` | `''` | Classification text displayed in footer |
| `index` | `number` | Required | Index of the card in a list |
| `small` | `boolean` | `true` | Whether to display card in small format |
| `contentType` | `string` | Required | Content type for routing |
| `remove` | `boolean` | `false` | Whether to show remove button |
| `isSelected` | `boolean` | `false` | Whether the card is selected |
| `delay` | `number` | `300` | Animation delay in milliseconds |

## Output Events

| Event | Type | Description |
|-------|------|-------------|
| `resized` | `EventEmitter<any>` | Emitted when card is resized (null=collapsed, index=expanded) |
| `crossClick` | `EventEmitter<any>` | Emitted when remove button is clicked |
| `selectIndicator` | `EventEmitter<{status: boolean, id: string, type: string}>` | Emitted when checkbox is toggled |

## Features

- **No API Calls**: Component receives all data via input properties
- **Expandable Charts**: Charts display when user clicks expand button
- **Flexible Data Structure**: Handles both DXP-specific and generic data formats
- **Chart Data Processing**: Automatically processes visualization config or uses direct chart data
- **Responsive Design**: Supports both small and expanded card views
- **Interactive Elements**: Includes resize, remove, and selection functionality
- **Type Safety**: Uses TypeScript interfaces for better development experience
- **DXP-Specific Design**: 
  - Displays component subtitle below the main title
  - Uses a dedicated DXP icon instead of domain-specific icons
  - Checkbox relocated to top-left header area for better UX
  - Custom styling optimized for DXP component display
  - Chart integration with expand/collapse functionality
