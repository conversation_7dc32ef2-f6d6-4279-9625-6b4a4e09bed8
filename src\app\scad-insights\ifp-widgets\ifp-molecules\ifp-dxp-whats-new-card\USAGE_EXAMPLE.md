# IFP DXP What's New Card Component Usage

## Overview
The `ifp-dxp-whats-new-card` component displays DXP component data in a card format. It accepts data as an input property instead of making direct API calls.

## Basic Usage

### In your component template:
```html
<ifp-dxp-whats-new-card 
  [data]="dxpComponentData"
  [contentType]="'dxp-component'"
  [index]="0"
  [small]="true"
  [remove]="true"
  [isSelected]="false"
  (crossClick)="onRemoveCard($event)"
  (selectIndicator)="onSelectIndicator($event)"
  (resized)="onCardResized($event)">
</ifp-dxp-whats-new-card>
```

### In your component TypeScript file:
```typescript
export class YourComponent {
  dxpComponentData = {
    "objectId": "1a9719ce-e74f-4889-8e7b-ff2ea24750ae",
    "component_title": "Sample DXP Component",
    "component_subtitle": "Sample subtitle",
    "sourceAssetId": "ZXukWa6eQiWW0xYNhBKZrA",
    "sourceProductId": "5Jpdhn9PTmuGh5H4QXQvEw",
    "visualizationConfig": {
      "source_filter": {
        "groups": [
          {
            "conditions": [],
            "operator": "and"
          }
        ],
        "global_operator": "and"
      },
      "chart_configuration": {
        "x_axis": {
          "label": "X Axis Label",
          "axis": {
            "column": "month",
            "data_type": "int",
            "aggregator": "sum"
          }
        },
        "y_axis": {
          "label": "Y Axis Label",
          "axis": {
            "column": "month_name",
            "data_type": "string",
            "aggregator": "count"
          }
        },
        "filterPanel": []
      }
    },
    "updated": "2025-08-05T13:25:31.037Z",
    "createdAt": "2025-08-05T13:25:31.037Z",
    "createdById": "7d5ad35a-c737-40c4-aac9-4128ea3d69e9",
    "createdBy": {
      "name": "John Doe",
      "designation": "specialist",
      "email": "<EMAIL>"
    },
    "approvalRequest": {
      "id": "01d8fdba-ad12-48d1-a03b-c6ca960f6688",
      "status": "pending"
    },
    // Optional additional properties
    "value": 1250,
    "unit": "units",
    "thresholdValue": 1000,
    "chartData": [/* chart data array */]
  };

  onRemoveCard(event: any) {
    console.log('Card removed:', event);
  }

  onSelectIndicator(event: any) {
    console.log('Indicator selected:', event);
  }

  onCardResized(event: any) {
    console.log('Card resized:', event);
  }
}
```

## Input Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `data` | `DxpComponentData \| null` | `null` | Main data object containing DXP component data |
| `classification` | `string` | `''` | Classification text displayed in footer |
| `index` | `number` | Required | Index of the card in a list |
| `small` | `boolean` | `true` | Whether to display card in small format |
| `contentType` | `string` | Required | Content type for routing |
| `remove` | `boolean` | `false` | Whether to show remove button |
| `isSelected` | `boolean` | `false` | Whether the card is selected |
| `delay` | `number` | `300` | Animation delay in milliseconds |

## Output Events

| Event | Type | Description |
|-------|------|-------------|
| `resized` | `EventEmitter<any>` | Emitted when card is resized |
| `crossClick` | `EventEmitter<any>` | Emitted when remove button is clicked |
| `selectIndicator` | `EventEmitter<{status: boolean, id: string, type: string}>` | Emitted when checkbox is toggled |

## Data Structure

The component expects data in the following format:

```typescript
interface DxpComponentData {
  objectId?: string;           // Unique identifier
  component_title?: string;    // Main title
  component_subtitle?: string; // Subtitle
  updated?: string;           // Last updated date
  createdAt?: string;         // Creation date
  createdBy?: {               // Creator information
    name: string;
    designation: string;
    email: string;
  };
  approvalRequest?: {         // Approval status
    id: string;
    status: string;
  };
  // Optional display properties
  value?: string | number;    // Main value to display
  unit?: string;             // Unit of measurement
  thresholdValue?: number;   // Threshold for comparison
  chartData?: any;           // Chart data for visualization
  // ... other optional properties
}
```

## Features

- **No API Calls**: Component receives all data via input properties
- **Flexible Data Structure**: Handles both DXP-specific and generic data formats
- **Responsive Design**: Supports both small and expanded card views
- **Interactive Elements**: Includes resize, remove, and selection functionality
- **Chart Support**: Can display charts when chart data is provided
- **Type Safety**: Uses TypeScript interfaces for better development experience
