export const dxpApi = {
  listEntityKpi: 'content-type/dxp/entity-kpi',
  entityKpiChart: (objectId: string) => `content-type/dxp/entity-kpi/${objectId}`,
  product: 'content-type/dxp/product',
  image: 'content-type/dxp/image/',
  // dataSet: 'content-type/dxp/asset/',
  column: (productId: string) => `content-type/dxp/product/${productId}/asset/`,
  productDetail: 'content-type/dxp/product/',
  query: (productId: string, assetId: string) => `content-type/dxp/product/${productId}/asset/${assetId}/query`,
  distinctValues: (productId: string, assetId: string) => `content-type/dxp/product/${productId}/asset/${assetId}/distinct-values`,
  userOnboardingUsers: 'user-onboarding/entity/users'
}
