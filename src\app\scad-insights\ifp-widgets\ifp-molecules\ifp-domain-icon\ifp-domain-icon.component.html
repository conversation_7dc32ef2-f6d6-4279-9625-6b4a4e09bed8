@if (useDxpIcon) {
  <!-- DXP-specific static icon -->
  <ifp-img
    [alt]="'DXP Component'"
    [darkIcon]="'assets/icons/dxp-whats-new-icon.png'"
    [lightIcon]="'assets/icons/dxp-whats-new-icon.png'"
    [tooltipText]="isTooltip ? 'DXP Component' : ''"
    [width]="width"
    [height]="height">
  </ifp-img>
} @else {
  <!-- Original domain icon behavior -->
  @if (domainIconSelector$ | async; as icon) {
    @if (icon.body) {
      @if (onlyWhite) {
        <ifp-img [alt]="domainName" [darkIcon]="icon.body.icon" [lightIcon]="icon.body.icon" [tooltipText]="isTooltip ? domainName : ''"></ifp-img>
      } @else {
        <ifp-img [alt]="domainName" [darkIcon]="icon.body.light_icon" [lightIcon]="icon.body.icon" [tooltipText]="isTooltip ? domainName : ''"></ifp-img>
      }
    }
  }
}


