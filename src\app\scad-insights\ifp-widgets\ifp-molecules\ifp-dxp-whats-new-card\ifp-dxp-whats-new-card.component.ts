import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, input, Input, InputSignal, model, ModelSignal, OnChanges, OnDestroy, OnInit, Output, Renderer2, SimpleChanges, ViewChild } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { IfpCardComponent } from '../../ifp-atoms/ifp-card/ifp-card.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonColor } from 'src/app/scad-insights/core/constants/button.constants';
import { IFPHighChartsComponent } from '../../charts/ifp-highcharts.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { ShortNumberPipe } from 'src/app/scad-insights/core/pipes/shortNumber.pipe';
import { IfpDomainIconComponent } from '../ifp-domain-icon/ifp-domain-icon.component';
import { IfpIconTextComponent } from '../ifp-icon-text/ifp-icon-text.component';
import { IfpCheckboxComponent } from '../../ifp-atoms/ifp-checkbox/ifp-checkbox.component';
import { animate, style, transition, trigger } from '@angular/animations';
import { fadeInOut } from 'src/app/scad-insights/animation/fade.animation';
import { analyticsClasses } from 'src/app/scad-insights/core/constants/analytics-class.constants';
import { QuotRemove } from 'src/app/scad-insights/core/pipes/quotsRemove.pipe';
import { IfpSyncButtonComponent, SyncStatus } from '../../ifp-atoms/ifp-sync-button/ifp-sync-button.component';
import { Router } from '@angular/router';

// DXP Data Structure Interface
export interface DxpComponentData {
  objectId?: string;
  id?: string;
  component_title?: string;
  title?: string;
  component_subtitle?: string;
  sourceAssetId?: string;
  sourceProductId?: string;
  visualizationConfig?: {
    source_filter: any;
    chart_configuration: any;
  };
  updated?: string;
  createdAt?: string;
  createdById?: string;
  createdBy?: {
    name: string;
    designation: string;
    email: string;
  };
  approvalRequest?: {
    id: string;
    status: string;
  };
  // Additional properties that might be present
  value?: string | number;
  range?: string;
  domain?: any;
  domains?: any[];
  chartData?: any;
  unit?: string;
  thresholdValue?: number;
  valueFormat?: string;
  compareFilters?: string[];
  baseDate?: string;
}

/**
 * DXP What's New Card Component
 *
 * This component displays DXP component data in a card format without making direct API calls.
 * Data should be passed via the @Input() data property.
 *
 * Usage:
 * <ifp-dxp-whats-new-card
 *   [data]="dxpComponentData"
 *   [contentType]="'dxp-component'"
 *   [index]="0"
 *   [small]="true">
 * </ifp-dxp-whats-new-card>
 */
@Component({
  selector: 'ifp-dxp-whats-new-card',
  templateUrl: './ifp-dxp-whats-new-card.component.html',
  styleUrl: './ifp-dxp-whats-new-card.component.scss',
  imports: [
    CommonModule,
    IfpCardComponent,
    IfpButtonComponent,
    TranslateModule,
    IFPHighChartsComponent,
    IfpTooltipDirective,
    ShortNumberPipe,
    IfpDomainIconComponent,
    IfpIconTextComponent,
    IfpCheckboxComponent,
    QuotRemove,
    IfpSyncButtonComponent
  ],
  providers: [ShortNumberPipe, QuotRemove, DatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('slideInOut', [
      transition(':enter', [
        style({ transform: 'translateY(-100%)' }),
        animate('200ms ease-in', style({ transform: 'translateY(0%)' }))
      ]),
      transition(':leave', [
        animate('200ms ease-in', style({ transform: 'translateY(-100%)', opacity: '0' }))
      ])
    ]),
    fadeInOut
  ]
})
export class IfpDxpWhatsNewCardComponent implements OnInit, OnDestroy, OnChanges {
  @ViewChild('chartRef') chartRef!: IFPHighChartsComponent;
  @ViewChild('card') card!: HTMLDivElement;

  // Core inputs - data passed directly instead of fetching via API
  @Input() data: DxpComponentData | null = null; // Main data object containing DXP component data
  @Input() classification: string = '';
  @Input() index!: number;
  @Input() small = true;
  @Input() contentType!: string;
  @Input() remove: boolean = false;
  @Input() isSelected: boolean = false;
  @Input() delay = 300;

  // Derived properties from data object
  public id: string = '';
  public title: string = '';
  public subtitle: string = '';
  public domain: any[] = [];
  public value: string = '';
  public range!: string;
  public baseDate!: any;

  // DXP specific inputs
  public isDxp: InputSignal<boolean> = input(false);
  public syncStatus: ModelSignal<SyncStatus> = model({status: 'false', action: 'sync'});

  // Events
  @Output() resized = new EventEmitter();
  @Output() crossClick = new EventEmitter();
  @Output() selectIndicator: EventEmitter<{ status: boolean; id: string | number; type: string }> = new EventEmitter<{ status: boolean; id: string | number; type: string }>();

  // Component properties
  public buttonClass = buttonClass;
  public buttonColor = buttonColor;
  public chart = false;
  public comparison!: string;
  public format!: string;
  public chartData: any = [];
  public domains: any[] = [];
  public name: string = '';
  public textLimit = 35;
  public analyticsClasses = analyticsClasses;

  constructor(
    private _elementRef: ElementRef,
    private _render: Renderer2,
    private _cdr: ChangeDetectorRef,
    private _router: Router,
    private _quotRemove: QuotRemove,
    private _datePipe: DatePipe
  ) {}

  ngOnInit(): void {
    this.initializeData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['small']) {
      this.handleResize(changes['small'].currentValue);
    }

    if (changes['data']) {
      this.initializeData();
    }
  }

  private initializeData(): void {
    if (this.data) {
      // Extract core properties from DXP data structure
      this.id = this.data.objectId || this.data.id || '';
      this.title = this.data.component_title || this.data.title || '';
      this.subtitle = this.data.component_subtitle || '';
      this.value = this.data.value?.toString() || '';
      this.range = this.data.range || '';
      this.domain = this.data.domain ? [this.data.domain] : (this.data.domains || []);

      // Set formatting and comparison data
      this.format = this.data.valueFormat?.split('_')[1] || this.format;
      this.comparison = this.data.compareFilters?.[0] || this.comparison;

      // Set chart data if available
      if (this.data.chartData) {
        this.chartData = this.data.chartData;
      }

      // Format base date - prioritize 'updated' field for DXP data
      if (this.data.updated) {
        this.baseDate = this._datePipe.transform(new Date(this.data.updated), 'dd/MM/yyyy');
      } else if (this.data.createdAt) {
        this.baseDate = this._datePipe.transform(new Date(this.data.createdAt), 'dd/MM/yyyy');
      } else if (this.data.baseDate) {
        this.baseDate = this._datePipe.transform(new Date(this.data.baseDate), 'dd/MM/yyyy');
      }

      // Handle DXP specific properties
      if (this.data.visualizationConfig) {
        // Extract chart configuration if needed
        const chartConfig = this.data.visualizationConfig.chart_configuration;
        if (chartConfig) {
          // You can process chart configuration here if needed for visualization
        }
      }

      // Handle approval status for DXP
      if (this.data.approvalRequest) {
        // You can use this for displaying approval status if needed
      }
    }

    // Set name with proper formatting
    this.name = this._quotRemove.transform(this.title);
    this.domains = this.domain?.length ? this.domain : [];

    this._cdr.detectChanges();
  }

  private handleResize(isSmall: boolean): void {
    if (isSmall) {
      setTimeout(() => {
        this.chart = false;
        this._cdr.detectChanges();
      }, this.delay);
      this._render.removeClass(this._elementRef.nativeElement.parentNode, 'ifp-active-card');
      this.resized.emit(null);
    } else {
      setTimeout(() => {
        this.chart = true;
        this._cdr.detectChanges();
      }, this.delay);
      this._render.addClass(this._elementRef.nativeElement.parentNode, 'ifp-active-card');
      this.resized.emit(this.index);
    }
  }

  /**
   * Resize card between small and large views
   */
  resize(value: boolean, index: number = this.index): void {
    this.small = value;
    this.handleResize(value);
  }

  /**
   * Remove card event
   */
  removeEvent(): void {
    this.crossClick.emit({
      id: this.id,
      domain: this.domain[0],
      contentType: this.contentType,
      title: this.title
    });
  }

  /**
   * Select/deselect node checkbox event
   */
  selectNode(event: any): void {
    this.selectIndicator.emit({
      status: event.target.checked,
      id: this.id,
      type: this.contentType
    });
  }

  /**
   * Open URL in new route
   */
  openUrl(url: string): void {
    const queryParams = {
      contentType: this.contentType
    };
    this._router.navigate([url], { queryParams: queryParams });
  }

  /**
   * Remove chart tooltip
   */
  removeTooltip(): void {
    if (this.chartRef) {
      this.chartRef.removeTooltip();
    }
  }

  /**
   * Check if value is below threshold
   */
  isValueBelowThreshold(): boolean {
    return !!(this.data?.value && this.data?.thresholdValue && +this.data.value < +this.data.thresholdValue);
  }

  /**
   * Get display value (either threshold or actual value)
   */
  getDisplayValue(): string {
    if (this.isValueBelowThreshold()) {
      return this.data?.thresholdValue?.toString() || this.value;
    }
    return this.value;
  }

  ngOnDestroy(): void {
    // Clean up any remaining subscriptions or timers if needed
  }
}
